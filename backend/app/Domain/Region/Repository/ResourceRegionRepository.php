<?php declare(strict_types = 1);

namespace App\Domain\Region\Repository;

use App\Domain\Region\RegionLevel;
use App\Domain\Region\ResourceRegion;
use App\Model\Utils\Position;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Query\ResultSetMappingBuilder;

/**
 * @method ResourceRegion|NULL find($id, ?int $lockMode = NULL, ?int $lockVersion = NULL)
 * @method ResourceRegion|NULL findOneBy(array<string, mixed> $criteria, ?array<string, string> $orderBy = NULL)
 * @method ResourceRegion[] findAll()
 * @method ResourceRegion[] findBy(array<string, mixed> $criteria, ?array<string, string> $orderBy = NULL, ?int $limit = NULL, ?int $offset = NULL)
 *
 * @extends EntityRepository<ResourceRegion>
 */
class ResourceRegionRepository extends EntityRepository {

	/**
	 * @return ResourceRegion[]
	 */
	public function findInRadius(Position $position, int $range) : array {
		$rsm = new ResultSetMappingBuilder($this->_em);
		$rsm->addRootEntityFromClassMetadata(ResourceRegion::class, 'gr');

		return $this
			->_em
			->createNativeQuery(
				// params is used to calculate the range in degrees
				// 111000 is the length of 1 degree in meters
				// 111000 * COS(RADIANS(:lat)) is the length of 1 degree in meters on the longitude
				// ST_MakeEnvelope is used to filter the results by the bounding box
				// ST_DWithin is used to filter the results by the range
				'
					WITH params AS (
						SELECT 
							CAST(:range AS FLOAT) / 111000 AS latitude_deg,
							CAST(:range AS FLOAT) / (111000 * COS(RADIANS(:lat))) AS longitude_deg
					)
					SELECT gr.*, ST_AsEWKT(gr.position) AS position 
					FROM game_region gr
					JOIN osm_polygon op ON op.osm_id = gr.osm_id
					CROSS JOIN params
					WHERE 
						op.way && ST_MakeEnvelope(
							:lng - params.longitude_deg, :lat - params.latitude_deg,
							:lng + params.longitude_deg, :lat + params.latitude_deg,
							4326
						) 
						AND ST_DWithin(
							op.way,
							ST_SetSRID(ST_MakePoint(:lng, :lat), 4326),
							GREATEST(params.latitude_deg, params.longitude_deg)
						)
						AND gr.level = :regionLevel;
        		',
				$rsm,
			)
			->setParameter('lng', $position->getLng())
			->setParameter('lat', $position->getLat())
			->setParameter('range', $range)
			->setParameter('regionLevel', RegionLevel::lvl6)
			->getResult();
	}

}