<?php declare(strict_types = 1);

namespace App\Domain\OAuth2\RefreshToken;

use App\Domain\OAuth2\AccessToken\AccessToken;
use App\Model\Database\Entity\AbstractEntity;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;
use League\OAuth2\Server\Entities\RefreshTokenEntityInterface;



#[ORM\Entity(repositoryClass: RefreshTokenRepository::class)]
#[ORM\Table(name: 'api_refresh_token')]
class RefreshToken extends AbstractEntity implements RefreshTokenEntityInterface{




	#[ORM\Id]
	#[ORM\Column(type: 'string', nullable: false)]
	protected string $identifier;

	#[ORM\ManyToOne(targetEntity: AccessToken::class)]
	#[ORM\JoinColumn(name: 'access_token_id', referencedColumnName: 'identifier', nullable: false, onDelete: 'CASCADE')]
	protected AccessToken $accessToken;

	/**
	 * @var DateTimeImmutable
	 */
	#[ORM\Column(type: 'datetime_immutable', nullable: false)]
	protected DateTimeImmutable $expiryDateTime;

	public function getIdentifier() : string {
		return $this->identifier;
	}

	public function setIdentifier($identifier) : void {
		if (!is_string($identifier) && !is_int($identifier)) {
			throw new \InvalidArgumentException('Identifier must be string or int');
		}
		$this->identifier = (string) $identifier;
	}

	public function getExpiryDateTime() : DateTimeImmutable {
		return $this->expiryDateTime;
	}

	public function setExpiryDateTime(DateTimeImmutable $dateTime) : void {
		$this->expiryDateTime = $dateTime;
	}

	public function getAccessToken() : AccessToken {
		return $this->accessToken;
	}

	public function setAccessToken($accessToken) : void {
		if (!$accessToken instanceof AccessToken) {
			throw new \InvalidArgumentException('Access token must be instance of AccessToken');
		}
		$this->accessToken = $accessToken;
	}

}