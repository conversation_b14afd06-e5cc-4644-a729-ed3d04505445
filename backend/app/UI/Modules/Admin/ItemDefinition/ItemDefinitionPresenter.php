<?php
declare(strict_types = 1);

namespace App\UI\Modules\Admin\ItemDefinition;

use App\Domain\Item\ItemAttribute;
use App\Domain\Item\ItemCategory;
use App\Domain\Item\ItemDefinition;
use App\Domain\Item\ItemDefinitionRepository;
use App\Domain\Item\ItemType;
use App\Domain\Item\Service\GroqItemDefinitionService;
use App\Model\Database\EntityManager;
use App\UI\DataGrid\DataGridFactory;
use App\UI\Form\BaseForm;
use App\UI\Form\FormFactory;
use App\UI\Modules\Admin\BaseAdminPresenter;
use Contributte\Datagrid\Column\Action\Confirmation\StringConfirmation;
use Contributte\Datagrid\Datagrid;
use Contributte\Datagrid\Exception\DatagridException;

final class ItemDefinitionPresenter extends BaseAdminPresenter {

	public ItemDefinitionRepository $itemDefinitionRepository;

	private ?ItemDefinition $itemDefinition = NULL;

	public function __construct(
		private readonly EntityManager 				$em,
		private readonly DataGridFactory 			$dataGridFactory,
		private readonly FormFactory 				$formFactory,
		private readonly GroqItemDefinitionService 	$groqItemDefinitionService,
	) {
		parent::__construct();
		$this->itemDefinitionRepository = $em->getRepository(ItemDefinition::class);
	}

	public function actionEdit(int $id) : void {
		$this->itemDefinition = $this->itemDefinitionRepository->find($id);
		if ($this->itemDefinition === null) {
			$this->redirect('list');
		}

		$this->addBreadcrumbItem('Editace definice itemu');
	}

	public function actionAdd() : void {
		$this->setView('edit');
		$this->addBreadcrumbItem('Přidání definice itemu');
	}

	public function actionGenerate(string $prompt) : void {
		$this->setView('edit');
		$values = $this->groqItemDefinitionService->generate($prompt);
		$editFormComponent = $this->getComponent('editForm');
		if ($editFormComponent instanceof BaseForm) {
			$editFormComponent->setDefaults($values);
		}
		$this->addBreadcrumbItem('Generování definice itemu');
	}

	public function handleDelete(int $id) : void {
		$item = $this->itemDefinitionRepository->find($id);
		if ($item !== null) {
			$this->em->remove($item);
			$this->em->flush();
			$this->flashMessage('Item byl smazán', 'success');
		} else {
			$this->flashMessage('Něco se nepovedlo :(', 'danger');
		}

		if ($this->isAjax()) {
			$gridComponent = $this->getComponent('itemGrid');
			if (method_exists($gridComponent, 'reload')) {
				$gridComponent->reload();
			}
			$this->redrawControl('flashes');
		} else {
			$this->redirect('this');
		}
	}

	public function renderList(): void
	{
		$this->addBreadcrumbItem('Definice itemů', $this->link('list'));
	}

	/**
	 * @throws DatagridException
	 */
	public function createComponentItemGrid() : DataGrid {
		$grid = $this->dataGridFactory->create();

		$grid->setDataSource($this->itemDefinitionRepository->createQueryBuilder('id'));

		$grid->addColumnText('name', 'Název')
			 ->setSortable()
			 ->setFilterText();

		$grid->addColumnText('category', 'Kategorie')
			 ->setSortable()
			 ->setRenderer(fn($item) => $item->getCategory()->value)
			 ->setFilterSelect(array_map(fn(ItemCategory $category) => $category->value, ItemCategory::cases()))
			 ->setPrompt('Vše');

		$grid->addColumnDateTime('created_at', 'Vytvořeno')
			 ->setFormat('d.m.Y H:i')
			 ->setSortable()
			 ->setFilterDate();

		$grid->addColumnText('attributes', 'Atributy')
			 ->setRenderer(fn($item) => implode(', ', $item->getAttributes()));

		$grid->addAction('edit', 'Editovat', 'edit')
			 ->setIcon('pencil')
			 ->setClass('btn btn-xs btn-primary')
			 ->setTitle('Editace');

		$grid->addAction('delete', 'Smazat', 'delete!')
			->setConfirmation(new StringConfirmation('Hohoho!!! Počkej! Opravdu chceš smazat tento item? Zmažeš tím i všechny jeho instance. !!! Tato akce je nevratná. !!!'))
			 ->setIcon('trash')
			 ->setClass('btn btn-xs btn-danger');

		return $grid;
	}

	public function createComponentEditForm() : BaseForm {
		$form = $this->formFactory->createAdmin();

		$form->addGroup(NULL);
		$types = array_map(fn(ItemType $type) => $type->value, ItemType::cases());
		$form->addSelect('type', 'Typ', array_combine($types, $types))
			 ->setRequired();

		$categories = array_map(fn(ItemCategory $category) => $category->value, ItemCategory::cases());
		$form->addSelect('category', 'Kategorie', array_combine($categories, $categories))
			 ->setRequired();

		$form->addText('name', 'Název')
			 ->setRequired();

		$form->addTextArea('description', 'Popis')
			 ->setRequired();

		$form->addInteger('minLevel', 'Minimální úroveň hrdiny')
			 ->addRule($form::Min, 'Hodnota musí být větší než 0', 1)
			 ->setNullable();

		$form->addCheckbox('spawnable', 'Spawnovatelný');

		$form->addInteger('commonness', 'Běžnost výskytu (0-100)')
			 ->setRequired()
			 ->addRule($form::Range, 'Hodnota musí být v rozmezí 0 až 100', [0, 100]);

		$form->addGroup('Atributy');
		$attributes = $form->addContainer('attributes');
		foreach (ItemAttribute::cases() as $attribute) {
			$attributes->addInteger($attribute->value, $attribute->name)
				->setHtmlAttribute('placeholder', 'nepoužito')
				->setNullable();
		}

		$form->addGroup(NULL);

		if ($this->itemDefinition !== null) {

			$form->addSubmit('submit', 'Uložit');

			$form->setDefaults([
				'type' => $this->itemDefinition->getType()->value,
				'category' => $this->itemDefinition->getCategory()->value,
				'name' => $this->itemDefinition->getName(),
				'description' => $this->itemDefinition->getDescription(),
				'minLevel' => $this->itemDefinition->getMinLevel(),
				'spawnable' => $this->itemDefinition->isSpawnable(),
				'commonness' => $this->itemDefinition->getCommonness(),
				'attributes' => $this->itemDefinition->getAttributes(),
			]);
		} else {
			$form->addSubmit('submit', 'Vytvořit');
		}

		$form->onSuccess[] = function (\stdClass $values) {
			$type = ItemType::from($values->type);
			$category = ItemCategory::from($values->category);

			if ($this->itemDefinition !== null) {
				$item = $this->itemDefinition;

				$item->setName($values->name);
				$item->setDescription($values->description);
				$item->setType($type);
				$item->setCategory($category);
			} else {
				$item = new ItemDefinition(
					$type,
					$category,
					$values->name,
					$values->description,
				);

				$this->em->persist($item);
			}

			$item->setMinLevel($values->minLevel);
			$item->setSpawnable($values->spawnable);
			$item->setCommonness($values->commonness);

			$item->clearAttributes();
			foreach ($values->attributes as $attribute => $value) {
				if ($value === null) {
					continue;
				}
				$item->addAttribute($attribute, $value);
			}

			$this->em->flush();

			$this->flashMessage('Item byl uložen', 'success');
			$this->redirect('list');
		};

		return $form;
	}

	protected function createComponentGenerate() : BaseForm {
		$form = $this->formFactory->createAdmin();

		$form->addText('prompt', 'Popis')
			 ->setRequired();

		$form->addSubmit('send', 'Generovat');

		$form->onSuccess[] = function (\stdClass $values) {
			$this->redirect('generate', ['prompt' => $values->prompt]);
		};

		return $form;
	}

}
