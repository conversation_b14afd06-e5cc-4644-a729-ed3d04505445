<?php declare(strict_types = 1);

namespace App\UI\Modules\Admin\UnitDefinition;

use App\Domain\Unit\UnitDefinition;
use App\Model\Database\EntityManager;
use App\UI\DataGrid\BaseDataGrid;
use App\UI\DataGrid\DataGridFactory;
use App\UI\Form\FormFactory;
use App\UI\Modules\Admin\BaseAdminPresenter;
use Nette\Application\UI\Form;

class UnitDefinitionPresenter extends BaseAdminPresenter {
	private EntityManager $entityManager;

	private DataGridFactory $dataGridFactory;

	private FormFactory $formFactory;

	private ?UnitDefinition $unitDefinition = NULL;

	public function __construct(
		EntityManager $entityManager,
		DataGridFactory $dataGridFactory,
		FormFactory $formFactory,
	) {
		parent::__construct();
		$this->entityManager = $entityManager;
		$this->dataGridFactory = $dataGridFactory;
		$this->formFactory = $formFactory;
	}

	public function actionNew() : void {
		$this->setView('edit');
	}

	public function actionEdit(int $id) : void {
		$this->unitDefinition = $this->entityManager->getRepository(UnitDefinition::class)->find($id);
	}

	public function renderList() : void {
		$template = $this->getTemplate();
		$template->title = 'Unit Definitions';
	}

	public function renderEdit() : void {
		$template = $this->getTemplate();
		$template->title = 'Edit Unit Definition';
		$template->unitDefinition = $this->unitDefinition;
	}

	public function createComponentUnitDefinitionsGrid() : BaseDataGrid {
		$grid = $this->dataGridFactory->create();

		$grid->setDataSource($this->entityManager->getRepository(UnitDefinition::class)->findAll());

		$grid->addColumnText('name', 'Název')
			 ->setSortable();

		$grid->addColumnText('description', 'Popis');

		$grid->addColumnText('size', 'Velikost')
			 ->setSortable();

		$grid->addColumnText('defence', 'Obrana')
			 ->setSortable();

		$grid->addColumnText('strength', 'Síla')
			 ->setSortable();

		$grid->addColumnText('agility', 'Obratnost')
			 ->setSortable();

		$grid->addColumnText('luck', 'Štěstí')
			 ->setSortable();

		$grid->addAction('edit', 'Upravit', 'edit')
			 ->setIcon('pencil');

		$grid->addToolbarButton('new', 'Nový')
			 ->setIcon('plus');

		return $grid;
	}

	public function createComponentEditForm() : Form {
		$form = $this->formFactory->createAdmin();

		$form->addText('name', 'Název')
			 ->setRequired();

		$form->addTextArea('description', 'Popis')
			 ->setRequired();

		$form->addInteger('size', 'Velikost')
			 ->setRequired()
			 ->setDefaultValue(1);

		$form->addInteger('defence', 'Obrana')
			 ->setRequired()
			 ->setDefaultValue(1);

		$form->addInteger('strength', 'Síla')
			 ->setRequired()
			 ->setDefaultValue(1);

		$form->addInteger('agility', 'Obratnost')
			 ->setRequired()
			 ->setDefaultValue(1);

		$form->addInteger('luck', 'Štěstí')
			 ->setRequired()
			 ->setDefaultValue(1);

		$form->addSubmit('send', 'Save');
		$form->onSuccess[] = [$this, 'editFormSucceeded'];

		if ($this->unitDefinition !== NULL) {
			$form->setDefaults([
				'name' => $this->unitDefinition->getName(),
				'description' => $this->unitDefinition->getDescription(),
				'size' => $this->unitDefinition->getSize(),
				'defence' => $this->unitDefinition->getDefence(),
				'strength' => $this->unitDefinition->getStrength(),
				'agility' => $this->unitDefinition->getAgility(),
				'luck' => $this->unitDefinition->getLuck(),
			]);
		}

		return $form;
	}

	/**
	 * @param array<string, mixed> $values
	 */
	public function editFormSucceeded(Form $form, array $values) : void {
		$unitDefinition = $this->unitDefinition;
		if ($unitDefinition === NULL) {
			$unitDefinition = new UnitDefinition($values['name'], $values['description']);
			$this->entityManager->persist($unitDefinition);
		}

		$unitDefinition->setName($values['name']);
		$unitDefinition->setDescription($values['description']);
		$unitDefinition->setSize($values['size']);
		$unitDefinition->setDefense($values['defence']);
		$unitDefinition->setStrength($values['strength']);
		$unitDefinition->setAgility($values['agility']);
		$unitDefinition->setLuck($values['luck']);

		$this->entityManager->flush();

		$this->flashMessage('Unit definition saved', 'success');

		$this->redirect('list');
	}
}