<?php

namespace App\UI\Control\Breadcrumb;

use Nette\Application\UI\Control;

class Breadcrumb extends Control
{
    /** @var array<int, array{label: string, link: string|null}> */
    private array $items = [];

    public function addItem(string $label, ?string $link = null): void
    {
        $this->items[] = ['label' => $label, 'link' => $link];
    }

    public function render(): void
    {
        $template = $this->getTemplate();
        /** @var \stdClass $template */
        $template->items = $this->items;
        $template->setFile(__DIR__ . '/Breadcrumb.latte');
        $template->render();
    }
}
