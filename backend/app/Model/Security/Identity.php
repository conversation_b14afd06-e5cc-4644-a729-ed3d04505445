<?php declare(strict_types = 1);

namespace App\Model\Security;

use Nette\Security\SimpleIdentity as NetteIdentity;

class Identity extends NetteIdentity {

	/** @var array<string, mixed> */
	private array $userData;

	/**
	 * @param mixed $id
	 * @param mixed $roles
	 * @param array<string, mixed> $data
	 */
	public function __construct($id, $roles = null, array $data = []) {
		parent::__construct($id, $roles, $data);
		$this->userData = $data;
	}

	public function getFullname() : string {
		$name = $this->userData['name'] ?? '';
		$surname = $this->userData['surname'] ?? '';

		// Ensure values are strings
		$nameStr = is_string($name) ? $name : (string) $name;
		$surnameStr = is_string($surname) ? $surname : (string) $surname;

		return sprintf('%s %s', $nameStr, $surnameStr);
	}

}
